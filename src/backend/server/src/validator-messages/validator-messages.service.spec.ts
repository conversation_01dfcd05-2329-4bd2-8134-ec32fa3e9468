import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { MessageType, MessageSource, PubSubMessage } from '../pubsub/interfaces/pubsub-messages.interface';
import { ValidatorMessage, ProcessingStatus } from './entities/validator-message.entity';
import { ValidatorMessageLookupService } from './lookup.service';
import { ValidatorMessagesService } from './validator-messages.service';

describe('ValidatorMessagesService', () => {
  let service: ValidatorMessagesService;

  const mockValidatorMessage: ValidatorMessage = {
    messageId: '1',
    messageTypeId: 1,
    processingStatusId: 1,
    validatorHotkey: 'test_validator_hotkey',
    minerHotkey: 'test_miner_hotkey',
    allocationUuid: 'test_allocation_uuid',
    occurredAt: new Date('2023-01-01T00:00:00Z'),
    createdAt: new Date('2023-01-01T00:00:00Z'),
    processedAt: null,
    errorMessage: null,
    validatorVerified: false,
    signatureValid: false,
    rawMessageData: {},
    messageTypeRef: undefined,
    processingStatusRef: undefined,
    get messageData() { return {} as PubSubMessage; },
    set messageData(value: PubSubMessage) { this.rawMessageData = value as unknown as Record<string, unknown>; },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    isPubSubMessage: jest.fn().mockReturnValue(true) as any,
    getMessageTypeString: jest.fn(),
    getProcessingStatusString: jest.fn(),
  };

  const mockPubSubMessage: PubSubMessage = {
    message_type: MessageType.GPU_DEALLOCATION,
    source: MessageSource.Backend,
    timestamp: '2023-01-01T00:00:00Z',
    data: {
      validator_hotkey: 'test_validator_hotkey',
      miner_hotkey: 'test_miner_hotkey',
      allocation_uuid: 'test_allocation_uuid',
      deallocation_reason: 'test_reason',
      deallocated_at: '2023-01-01T00:00:00Z',
    },
  };

  const mockRepository = {
    save: jest.fn(),
    find: jest.fn(),
    update: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockQueryBuilder = {
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    getMany: jest.fn(),
  };

  const mockLookupService = {
    mapMessageTypeToId: jest.fn(),
    mapProcessingStatusToId: jest.fn(),
    getMessageTypeString: jest.fn(),
    getProcessingStatusString: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ValidatorMessagesService,
        {
          provide: getRepositoryToken(ValidatorMessage),
          useValue: mockRepository,
        },
        {
          provide: ValidatorMessageLookupService,
          useValue: mockLookupService,
        },
      ],
    }).compile();

    service = module.get<ValidatorMessagesService>(ValidatorMessagesService);

    // Setup default mock returns
    mockLookupService.mapMessageTypeToId.mockReturnValue(1);
    mockLookupService.mapProcessingStatusToId.mockReturnValue(1);
    mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
    mockQueryBuilder.getMany.mockResolvedValue([mockValidatorMessage]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('storeMessage', () => {
    it('should store a message successfully', async () => {
      mockRepository.save.mockResolvedValue(mockValidatorMessage);

      const result = await service.storeMessage(mockPubSubMessage);

      expect(mockLookupService.mapMessageTypeToId).toHaveBeenCalledWith(MessageType.GPU_DEALLOCATION);
      expect(mockLookupService.mapProcessingStatusToId).toHaveBeenCalledWith(ProcessingStatus.PENDING);
      expect(mockRepository.save).toHaveBeenCalled();
      expect(result).toEqual(mockValidatorMessage);
    });

    it('should handle messages with different message types', async () => {
      const statusChangeMessage: PubSubMessage = {
        message_type: MessageType.GPU_STATUS_CHANGE,
        source: MessageSource.Backend,
        timestamp: '2023-01-01T00:00:00Z',
        data: {
          validator_hotkey: 'test_validator_hotkey',
          miner_hotkey: 'test_miner_hotkey',
          previous_status: 'busy',
          reason: 'test_reason',
          status_change_at: '2023-01-01T00:00:00Z',
        },
      };

      mockRepository.save.mockResolvedValue(mockValidatorMessage);

      await service.storeMessage(statusChangeMessage);

      expect(mockRepository.save).toHaveBeenCalled();
    });

    it('should handle messages without allocation UUID', async () => {
      const messageWithoutAllocation: PubSubMessage = {
        message_type: MessageType.GPU_DEALLOCATION,
        source: MessageSource.Backend,
        timestamp: '2023-01-01T00:00:00Z',
        data: {
          validator_hotkey: 'test_validator_hotkey',
          miner_hotkey: 'test_miner_hotkey',
          deallocation_reason: 'test_reason',
          deallocated_at: '2023-01-01T00:00:00Z',
          // No allocation_uuid
        },
      };

      mockRepository.save.mockResolvedValue(mockValidatorMessage);

      await service.storeMessage(messageWithoutAllocation);

      expect(mockRepository.save).toHaveBeenCalled();
    });

    it('should throw error when save fails', async () => {
      const error = new Error('Database error');
      mockRepository.save.mockRejectedValue(error);

      await expect(service.storeMessage(mockPubSubMessage)).rejects.toThrow('Database error');
    });
  });

  describe('getMessagesByType', () => {
    it('should get messages by type successfully', async () => {
      const result = await service.getMessagesByType(MessageType.GPU_DEALLOCATION);

      expect(mockLookupService.mapMessageTypeToId).toHaveBeenCalledWith(MessageType.GPU_DEALLOCATION);
      expect(mockRepository.createQueryBuilder).toHaveBeenCalledWith('vm');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('vm.message_type_id = :message_type_id', { message_type_id: 1 });
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('vm.occurred_at', 'DESC');
      expect(mockQueryBuilder.limit).toHaveBeenCalledWith(100);
      expect(result).toEqual([mockValidatorMessage]);
    });

    it('should apply date filters when provided', async () => {
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-02');

      await service.getMessagesByType(MessageType.GPU_DEALLOCATION, startDate, endDate);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'vm.occurred_at BETWEEN :startDate AND :endDate',
        { startDate, endDate }
      );
    });

    it('should apply custom limit', async () => {
      await service.getMessagesByType(MessageType.GPU_DEALLOCATION, undefined, undefined, 50);

      expect(mockQueryBuilder.limit).toHaveBeenCalledWith(50);
    });
  });

  describe('getMessagesByValidator', () => {
    it('should get messages by validator hotkey', async () => {
      const validatorHotkey = 'test_validator';

      const result = await service.getMessagesByValidator(validatorHotkey);

      expect(mockRepository.createQueryBuilder).toHaveBeenCalledWith('vm');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('vm.validator_hotkey = :validator_hotkey', { validator_hotkey: validatorHotkey });
      expect(result).toEqual([mockValidatorMessage]);
    });

    it('should filter by message types when provided', async () => {
      const validatorHotkey = 'test_validator';
      const messageTypes = [MessageType.GPU_DEALLOCATION, MessageType.POG_RESULT];
      mockLookupService.mapMessageTypeToId.mockReturnValueOnce(1).mockReturnValueOnce(3);

      await service.getMessagesByValidator(validatorHotkey, messageTypes);

      expect(mockLookupService.mapMessageTypeToId).toHaveBeenCalledTimes(2);
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'vm.message_type_id IN (:...message_type_ids)',
        { message_type_ids: [1, 3] }
      );
    });
  });

  describe('getMessagesByMiner', () => {
    it('should get messages by miner hotkey', async () => {
      const minerHotkey = 'test_miner';

      const result = await service.getMessagesByMiner(minerHotkey);

      expect(mockRepository.createQueryBuilder).toHaveBeenCalledWith('vm');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('vm.miner_hotkey = :miner_hotkey', { miner_hotkey: minerHotkey });
      expect(result).toEqual([mockValidatorMessage]);
    });

    it('should filter by message types when provided', async () => {
      const minerHotkey = 'test_miner';
      const messageTypes = [MessageType.GPU_DEALLOCATION];
      mockLookupService.mapMessageTypeToId.mockReturnValue(1);

      await service.getMessagesByMiner(minerHotkey, messageTypes);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'vm.message_type_id IN (:...message_type_ids)',
        { message_type_ids: [1] }
      );
    });
  });

  describe('getMessagesByAllocation', () => {
    it('should get messages by allocation UUID', async () => {
      const allocationUuid = 'test_allocation';
      mockRepository.find.mockResolvedValue([mockValidatorMessage]);

      const result = await service.getMessagesByAllocation(allocationUuid);

      expect(mockRepository.find).toHaveBeenCalledWith({
        where: { allocationUuid },
        order: { occurredAt: 'ASC' },
      });
      expect(result).toEqual([mockValidatorMessage]);
    });
  });

  describe('markAsProcessed', () => {
    it('should mark message as processed', async () => {
      const messageId = '123';
      mockLookupService.mapProcessingStatusToId.mockReturnValue(2);

      await service.markAsProcessed(messageId);

      expect(mockLookupService.mapProcessingStatusToId).toHaveBeenCalledWith(ProcessingStatus.PROCESSED);
      expect(mockRepository.update).toHaveBeenCalledWith(messageId, {
        processingStatusId: 2,
        processedAt: expect.any(Date),
      });
    });
  });

  describe('markAsFailed', () => {
    it('should mark message as failed', async () => {
      const messageId = '123';
      const errorMessage = 'Test error';
      mockLookupService.mapProcessingStatusToId.mockReturnValue(3);

      await service.markAsFailed(messageId, errorMessage);

      expect(mockLookupService.mapProcessingStatusToId).toHaveBeenCalledWith(ProcessingStatus.FAILED);
      expect(mockRepository.update).toHaveBeenCalledWith(messageId, {
        processingStatusId: 3,
        processedAt: expect.any(Date),
        errorMessage,
      });
    });
  });

  describe('getPendingMessages', () => {
    it('should get pending messages', async () => {
      mockLookupService.mapProcessingStatusToId.mockReturnValue(1);
      mockRepository.find.mockResolvedValue([mockValidatorMessage]);

      const result = await service.getPendingMessages();

      expect(mockLookupService.mapProcessingStatusToId).toHaveBeenCalledWith(ProcessingStatus.PENDING);
      expect(mockRepository.find).toHaveBeenCalledWith({
        where: { processingStatusId: 1 },
        order: { createdAt: 'ASC' },
        take: 50,
      });
      expect(result).toEqual([mockValidatorMessage]);
    });

    it('should apply custom limit', async () => {
      mockLookupService.mapProcessingStatusToId.mockReturnValue(1);
      mockRepository.find.mockResolvedValue([]);

      await service.getPendingMessages(25);

      expect(mockRepository.find).toHaveBeenCalledWith({
        where: { processingStatusId: 1 },
        order: { createdAt: 'ASC' },
        take: 25,
      });
    });
  });
});
