import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MessageData, MessageType, PubSubMessage } from '../pubsub/interfaces/pubsub-messages.interface';
import { MessageTypeId, ProcessingStatusId } from './entities';
import { ValidatorMessage, ProcessingStatus } from './entities/validator-message.entity';
import { ValidatorMessageLookupService } from './lookup.service';

@Injectable()
export class ValidatorMessagesService {
  private readonly logger = new Logger(ValidatorMessagesService.name);

  constructor(
    @InjectRepository(ValidatorMessage)
    private validatorMessageRepository: Repository<ValidatorMessage>,
    private lookupService: ValidatorMessageLookupService,
  ) {}

  /**
   * Store a validator message in the database
   */
  async storeMessage(message: PubSubMessage): Promise<ValidatorMessage> {
    try {
      const validatorMessage = new ValidatorMessage();

      // Map message type to ID using lookup service
      const messageType = this.mapMessageType(message.message_type);
      validatorMessage.messageTypeId = this.lookupService.mapMessageTypeToId(messageType);

      // Set default processing status to pending
      validatorMessage.processingStatusId = this.lookupService.mapProcessingStatusToId(ProcessingStatus.PENDING);

      validatorMessage.validatorHotkey = this.extractvalidator_hotkey(message);
      validatorMessage.occurredAt = new Date(message.timestamp);
      validatorMessage.messageData = message;

      // Extract common fields based on message type
      if (this.hasMinerHotkey(message)) {
        validatorMessage.minerHotkey = this.extractMinerHotkey(message);
      }

      if (this.hasAllocationUuid(message)) {
        validatorMessage.allocationUuid = this.extractAllocationUuid(message);
      }

      const savedMessage = await this.validatorMessageRepository.save(validatorMessage);
      this.logger.log(`Stored ${message.message_type} message from validator ${validatorMessage.validatorHotkey}`);

      return savedMessage;
    } catch (error) {
      this.logger.error(`Failed to store validator message: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get messages by type and time range
   */
  async getMessagesByType(message_type: MessageType, startDate?: Date, endDate?: Date, limit = 100): Promise<ValidatorMessage[]> {
    const messageTypeId = this.lookupService.mapMessageTypeToId(message_type);

    const query = this.validatorMessageRepository
      .createQueryBuilder('vm')
      .where('vm.message_type_id = :message_type_id', { message_type_id: messageTypeId })
      .orderBy('vm.occurred_at', 'DESC')
      .limit(limit);

    if (startDate && endDate) {
      query.andWhere('vm.occurred_at BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    return await query.getMany();
  }

  /**
   * Get messages by validator hotkey
   */
  async getMessagesByValidator(validator_hotkey: string, message_types?: MessageType[], limit = 100): Promise<ValidatorMessage[]> {
    const query = this.validatorMessageRepository
      .createQueryBuilder('vm')
      .where('vm.validator_hotkey = :validator_hotkey', { validator_hotkey })
      .orderBy('vm.occurred_at', 'DESC')
      .limit(limit);

    if (message_types && message_types.length > 0) {
      const messageTypeIds = message_types.map(mt => this.lookupService.mapMessageTypeToId(mt));
      query.andWhere('vm.message_type_id IN (:...message_type_ids)', { message_type_ids: messageTypeIds });
    }

    return await query.getMany();
  }

  /**
   * Get messages by miner hotkey
   */
  async getMessagesByMiner(miner_hotkey: string, message_types?: MessageType[], limit = 100): Promise<ValidatorMessage[]> {
    const query = this.validatorMessageRepository
      .createQueryBuilder('vm')
      .where('vm.miner_hotkey = :miner_hotkey', { miner_hotkey })
      .orderBy('vm.occurred_at', 'DESC')
      .limit(limit);

    if (message_types && message_types.length > 0) {
      const messageTypeIds = message_types.map(mt => this.lookupService.mapMessageTypeToId(mt));
      query.andWhere('vm.message_type_id IN (:...message_type_ids)', { message_type_ids: messageTypeIds });
    }

    return await query.getMany();
  }

  /**
   * Get messages by allocation UUID
   */
  async getMessagesByAllocation(allocation_uuid: string): Promise<ValidatorMessage[]> {
    return await this.validatorMessageRepository.find({
      where: { allocationUuid: allocation_uuid },
      order: { occurredAt: 'ASC' },
    });
  }

  /**
   * Mark message as processed
   */
  async markAsProcessed(message_id: string): Promise<void> {
    await this.validatorMessageRepository.update(message_id, {
      processingStatusId: this.lookupService.mapProcessingStatusToId(ProcessingStatus.PROCESSED),
      processedAt: new Date(),
    });
  }

  /**
   * Mark message as failed
   */
  async markAsFailed(message_id: string, error_message: string): Promise<void> {
    await this.validatorMessageRepository.update(message_id, {
      processingStatusId: this.lookupService.mapProcessingStatusToId(ProcessingStatus.FAILED),
      processedAt: new Date(),
      errorMessage: error_message,
    });
  }

  /**
   * Get pending messages for processing
   */
  async getPendingMessages(limit = 50): Promise<ValidatorMessage[]> {
    const pendingStatusId = this.lookupService.mapProcessingStatusToId(ProcessingStatus.PENDING);
    return await this.validatorMessageRepository.find({
      where: { processingStatusId: pendingStatusId },
      order: { createdAt: 'ASC' },
      take: limit,
    });
  }

  // Helper methods
  private mapMessageType(message_type: string): MessageType {
    if (Object.values(MessageType).includes(message_type as MessageType)) {
      return message_type as MessageType;
    }
    throw new Error(`Unknown message type to map: ${message_type}`);
  }

  private extractvalidator_hotkey(message: PubSubMessage): string {
    return (message.data ?? ({} as MessageData))?.validator_hotkey || '';
  }

  private extractMinerHotkey(message: PubSubMessage): string {
    return (message.data ?? ({} as MessageData))?.miner_hotkey || '';
  }

  private extractAllocationUuid(message: PubSubMessage): string {
    return (message.data ?? ({} as MessageData))?.allocation_uuid || '';
  }

  private hasMinerHotkey(message: PubSubMessage): boolean {
    return !!(message.data ?? ({} as MessageData))?.miner_hotkey;
  }

  private hasAllocationUuid(message: PubSubMessage): boolean {
    return !!(message.data ?? ({} as MessageData))?.allocation_uuid;
  }
}
