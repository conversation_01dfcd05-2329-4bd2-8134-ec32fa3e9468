import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SupabaseModule } from '../core';
import { EmailModule } from '../email/email.module';
import { GpuModule } from '../gpu';
import { GpusRented } from '../gpu/entities';
import { GpuUtilityModule } from '../gpu-utility/gpu-utility.module';
import { NotificationModule } from '../notification';
import { PaymentModule } from '../payment';
import { RedisModule } from '../redis';
import { User } from '../user';
import { ValidatorMessage, ValidatorMessageTypeLu, ValidatorProcessingStatusLu } from './entities';
import { ValidatorMessageLookupService } from './lookup.service';
import { MessageDeduplicationService } from './message-deduplication.service';
import { ValidatorMessagesService } from './validator-messages.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ValidatorMessage,
      ValidatorMessageTypeLu,
      ValidatorProcessingStatusLu,
      GpusRented,
      User
    ]),
    NotificationModule,
    EmailModule,
    forwardRef(() => GpuModule),
    GpuUtilityModule,
    PaymentModule,
    SupabaseModule,
    ConfigModule,
    RedisModule,
  ],
  providers: [ValidatorMessagesService, ValidatorMessageLookupService, MessageDeduplicationService],
  exports: [ValidatorMessagesService, ValidatorMessageLookupService, MessageDeduplicationService],
})
export class ValidatorMessagesModule {}
