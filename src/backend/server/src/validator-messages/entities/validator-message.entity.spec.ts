import { MessageType, MessageSource, PubSubMessage } from '../../pubsub/interfaces/pubsub-messages.interface';
import { ValidatorMessageTypeLu, ValidatorProcessingStatusLu } from './validator-message-lookup.entity';
import { ValidatorMessage, ProcessingStatus } from './validator-message.entity';

describe('ValidatorMessage Entity', () => {
  let validatorMessage: ValidatorMessage;

  beforeEach(() => {
    validatorMessage = new ValidatorMessage();
  });

  describe('messageData getter/setter', () => {
    it('should set and get message data correctly', () => {
      const testMessage: PubSubMessage = {
        message_type: MessageType.GPU_DEALLOCATION,
        source: MessageSource.Backend,
        timestamp: '2023-01-01T00:00:00Z',
        data: {
          validator_hotkey: 'test_validator',
          miner_hotkey: 'test_miner',
          deallocation_reason: 'test_reason',
          deallocated_at: '2023-01-01T00:00:00Z',
        },
      };

      validatorMessage.messageData = testMessage;
      const result = validatorMessage.messageData;

      expect(result).toEqual(testMessage);
      expect(validatorMessage.rawMessageData).toEqual(testMessage);
    });
  });

  describe('isPubSubMessage', () => {
    it('should return true for valid PubSubMessage', () => {
      const validMessage = {
        message_type: 'gpu_deallocation',
        source: 'backend',
        timestamp: '2023-01-01T00:00:00Z',
        data: {
          validator_hotkey: 'test_validator',
        },
      };

      const result = validatorMessage.isPubSubMessage(validMessage);
      expect(result).toBe(true);
    });

    it('should return false for invalid message - missing required fields', () => {
      const invalidMessage = {
        message_type: 'gpu_deallocation',
        // missing source, timestamp, data
      };

      const result = validatorMessage.isPubSubMessage(invalidMessage);
      expect(result).toBe(false);
    });

    it('should return false for null or undefined', () => {
      expect(validatorMessage.isPubSubMessage(null)).toBe(false);
      expect(validatorMessage.isPubSubMessage(undefined)).toBe(false);
    });

    it('should return false for non-object types', () => {
      expect(validatorMessage.isPubSubMessage('string')).toBe(false);
      expect(validatorMessage.isPubSubMessage(123)).toBe(false);
      expect(validatorMessage.isPubSubMessage(true)).toBe(false);
    });

    it('should return false when data is null', () => {
      const invalidMessage = {
        message_type: 'gpu_deallocation',
        source: 'backend',
        timestamp: '2023-01-01T00:00:00Z',
        data: null,
      };

      const result = validatorMessage.isPubSubMessage(invalidMessage);
      expect(result).toBe(false);
    });

    it('should return false when fields have wrong types', () => {
      const invalidMessage = {
        message_type: 123, // should be string
        source: 'backend',
        timestamp: '2023-01-01T00:00:00Z',
        data: {},
      };

      const result = validatorMessage.isPubSubMessage(invalidMessage);
      expect(result).toBe(false);
    });
  });

  describe('getMessageTypeString', () => {
    it('should return message type string from lookup reference', () => {
      const messageTypeLookup: ValidatorMessageTypeLu = {
        messageTypeId: 1,
        messageType: 'gpu_deallocation',
      };

      validatorMessage.messageTypeRef = messageTypeLookup;

      const result = validatorMessage.getMessageTypeString();
      expect(result).toBe('gpu_deallocation');
    });

    it('should return undefined when no lookup reference exists', () => {
      validatorMessage.messageTypeRef = undefined;

      const result = validatorMessage.getMessageTypeString();
      expect(result).toBeUndefined();
    });
  });

  describe('getProcessingStatusString', () => {
    it('should return processing status string from lookup reference', () => {
      const processingStatusLookup: ValidatorProcessingStatusLu = {
        processingStatusId: 1,
        processingStatus: 'pending',
      };

      validatorMessage.processingStatusRef = processingStatusLookup;

      const result = validatorMessage.getProcessingStatusString();
      expect(result).toBe('pending');
    });

    it('should return undefined when no lookup reference exists', () => {
      validatorMessage.processingStatusRef = undefined;

      const result = validatorMessage.getProcessingStatusString();
      expect(result).toBeUndefined();
    });
  });

  describe('Entity properties', () => {
    it('should have correct foreign key properties', () => {
      validatorMessage.messageTypeId = 1;
      validatorMessage.processingStatusId = 2;

      expect(validatorMessage.messageTypeId).toBe(1);
      expect(validatorMessage.processingStatusId).toBe(2);
    });

    it('should have correct basic properties', () => {
      const now = new Date();

      validatorMessage.validatorHotkey = 'test_validator';
      validatorMessage.minerHotkey = 'test_miner';
      validatorMessage.allocationUuid = 'test_allocation';
      validatorMessage.occurredAt = now;
      validatorMessage.createdAt = now;

      expect(validatorMessage.validatorHotkey).toBe('test_validator');
      expect(validatorMessage.minerHotkey).toBe('test_miner');
      expect(validatorMessage.allocationUuid).toBe('test_allocation');
      expect(validatorMessage.occurredAt).toBe(now);
      expect(validatorMessage.createdAt).toBe(now);
    });

    it('should handle optional properties', () => {
      validatorMessage.processedAt = null;
      validatorMessage.errorMessage = null;

      expect(validatorMessage.processedAt).toBeNull();
      expect(validatorMessage.errorMessage).toBeNull();
    });
  });

  describe('Lookup entity relationships', () => {
    it('should properly set message type lookup reference', () => {
      const messageTypeLookup: ValidatorMessageTypeLu = {
        messageTypeId: 3,
        messageType: 'pog_result',
      };

      validatorMessage.messageTypeRef = messageTypeLookup;

      expect(validatorMessage.messageTypeRef).toBe(messageTypeLookup);
      expect(validatorMessage.getMessageTypeString()).toBe('pog_result');
    });

    it('should properly set processing status lookup reference', () => {
      const processingStatusLookup: ValidatorProcessingStatusLu = {
        processingStatusId: 2,
        processingStatus: 'processed',
      };

      validatorMessage.processingStatusRef = processingStatusLookup;

      expect(validatorMessage.processingStatusRef).toBe(processingStatusLookup);
      expect(validatorMessage.getProcessingStatusString()).toBe('processed');
    });
  });
});

describe('ProcessingStatus Enum', () => {
  it('should have correct enum values', () => {
    expect(ProcessingStatus.PENDING).toBe('pending');
    expect(ProcessingStatus.PROCESSED).toBe('processed');
    expect(ProcessingStatus.FAILED).toBe('failed');
  });

  it('should have all expected enum keys', () => {
    const keys = Object.keys(ProcessingStatus);
    expect(keys).toContain('PENDING');
    expect(keys).toContain('PROCESSED');
    expect(keys).toContain('FAILED');
    expect(keys).toHaveLength(3);
  });
});

describe('ProcessingStatus Enum', () => {
  it('should have correct enum values', () => {
    expect(ProcessingStatus.PENDING).toBe('pending');
    expect(ProcessingStatus.PROCESSED).toBe('processed');
    expect(ProcessingStatus.FAILED).toBe('failed');
  });

  it('should have all expected enum keys', () => {
    const keys = Object.keys(ProcessingStatus);
    expect(keys).toContain('PENDING');
    expect(keys).toContain('PROCESSED');
    expect(keys).toContain('FAILED');
    expect(keys).toHaveLength(3);
  });
});
