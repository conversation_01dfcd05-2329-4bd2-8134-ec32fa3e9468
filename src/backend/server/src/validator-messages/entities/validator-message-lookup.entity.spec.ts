import { 
  ValidatorMessageTypeLu, 
  ValidatorProcessingStatusLu,
  MESSAGE_TYPE_IDS,
  PROCESSING_STATUS_IDS,
  MessageTypeId,
  ProcessingStatusId
} from './validator-message-lookup.entity';

describe('ValidatorMessageTypeLu Entity', () => {
  let messageTypeLu: ValidatorMessageTypeLu;

  beforeEach(() => {
    messageTypeLu = new ValidatorMessageTypeLu();
  });

  it('should create instance with correct properties', () => {
    messageTypeLu.messageTypeId = 1;
    messageTypeLu.messageType = 'gpu_deallocation';

    expect(messageTypeLu.messageTypeId).toBe(1);
    expect(messageTypeLu.messageType).toBe('gpu_deallocation');
  });

  it('should handle all expected message types', () => {
    const expectedTypes = [
      { id: 1, type: 'gpu_deallocation' },
      { id: 2, type: 'gpu_status_change' },
      { id: 3, type: 'validator_status' },
      { id: 4, type: 'allocation_request' },
      { id: 5, type: 'pog_result' },
      { id: 6, type: 'miner_discovery' },
      { id: 7, type: 'miner_allocation' },
      { id: 8, type: 'miner_deallocation' },
    ];

    expectedTypes.forEach(({ id, type }) => {
      const entity = new ValidatorMessageTypeLu();
      entity.messageTypeId = id;
      entity.messageType = type;

      expect(entity.messageTypeId).toBe(id);
      expect(entity.messageType).toBe(type);
    });
  });
});

describe('ValidatorProcessingStatusLu Entity', () => {
  let processingStatusLu: ValidatorProcessingStatusLu;

  beforeEach(() => {
    processingStatusLu = new ValidatorProcessingStatusLu();
  });

  it('should create instance with correct properties', () => {
    processingStatusLu.processingStatusId = 1;
    processingStatusLu.processingStatus = 'pending';

    expect(processingStatusLu.processingStatusId).toBe(1);
    expect(processingStatusLu.processingStatus).toBe('pending');
  });

  it('should handle all expected processing statuses', () => {
    const expectedStatuses = [
      { id: 1, status: 'pending' },
      { id: 2, status: 'processed' },
      { id: 3, status: 'failed' },
    ];

    expectedStatuses.forEach(({ id, status }) => {
      const entity = new ValidatorProcessingStatusLu();
      entity.processingStatusId = id;
      entity.processingStatus = status;

      expect(entity.processingStatusId).toBe(id);
      expect(entity.processingStatus).toBe(status);
    });
  });
});

describe('MESSAGE_TYPE_IDS Constants', () => {
  it('should have correct constant values', () => {
    expect(MESSAGE_TYPE_IDS.GPU_DEALLOCATION).toBe(1);
    expect(MESSAGE_TYPE_IDS.GPU_STATUS_CHANGE).toBe(2);
    expect(MESSAGE_TYPE_IDS.VALIDATOR_STATUS).toBe(3);
    expect(MESSAGE_TYPE_IDS.ALLOCATION_REQUEST).toBe(4);
    expect(MESSAGE_TYPE_IDS.POG_RESULT).toBe(5);
    expect(MESSAGE_TYPE_IDS.MINER_DISCOVERY).toBe(6);
    expect(MESSAGE_TYPE_IDS.MINER_ALLOCATION).toBe(7);
    expect(MESSAGE_TYPE_IDS.MINER_DEALLOCATION).toBe(8);
  });

  it('should have all expected constant keys', () => {
    const keys = Object.keys(MESSAGE_TYPE_IDS);
    expect(keys).toContain('GPU_DEALLOCATION');
    expect(keys).toContain('GPU_STATUS_CHANGE');
    expect(keys).toContain('VALIDATOR_STATUS');
    expect(keys).toContain('ALLOCATION_REQUEST');
    expect(keys).toContain('POG_RESULT');
    expect(keys).toContain('MINER_DISCOVERY');
    expect(keys).toContain('MINER_ALLOCATION');
    expect(keys).toContain('MINER_DEALLOCATION');
    expect(keys).toHaveLength(8);
  });

  it('should have unique values', () => {
    const values = Object.values(MESSAGE_TYPE_IDS);
    const uniqueValues = [...new Set(values)];
    expect(values).toHaveLength(uniqueValues.length);
  });
});

describe('PROCESSING_STATUS_IDS Constants', () => {
  it('should have correct constant values', () => {
    expect(PROCESSING_STATUS_IDS.PENDING).toBe(1);
    expect(PROCESSING_STATUS_IDS.PROCESSED).toBe(2);
    expect(PROCESSING_STATUS_IDS.FAILED).toBe(3);
  });

  it('should have all expected constant keys', () => {
    const keys = Object.keys(PROCESSING_STATUS_IDS);
    expect(keys).toContain('PENDING');
    expect(keys).toContain('PROCESSED');
    expect(keys).toContain('FAILED');
    expect(keys).toHaveLength(3);
  });

  it('should have unique values', () => {
    const values = Object.values(PROCESSING_STATUS_IDS);
    const uniqueValues = [...new Set(values)];
    expect(values).toHaveLength(uniqueValues.length);
  });
});

describe('Type Definitions', () => {
  it('should accept valid MessageTypeId values', () => {
    const validIds: MessageTypeId[] = [1, 2, 3, 4, 5, 6, 7, 8];

    validIds.forEach(id => {
      expect(typeof id).toBe('number');
      expect(Object.values(MESSAGE_TYPE_IDS)).toContain(id);
    });
  });

  it('should accept valid ProcessingStatusId values', () => {
    const validIds: ProcessingStatusId[] = [1, 2, 3];
    
    validIds.forEach(id => {
      expect(typeof id).toBe('number');
      expect(Object.values(PROCESSING_STATUS_IDS)).toContain(id);
    });
  });
});

describe('Constants Integration', () => {
  it('should have MESSAGE_TYPE_IDS that match expected database values', () => {
    // These should match the actual database lookup table values
    const expectedMappings = [
      { constant: MESSAGE_TYPE_IDS.GPU_DEALLOCATION, expectedId: 1 },
      { constant: MESSAGE_TYPE_IDS.GPU_STATUS_CHANGE, expectedId: 2 },
      { constant: MESSAGE_TYPE_IDS.VALIDATOR_STATUS, expectedId: 3 },
      { constant: MESSAGE_TYPE_IDS.ALLOCATION_REQUEST, expectedId: 4 },
      { constant: MESSAGE_TYPE_IDS.POG_RESULT, expectedId: 5 },
      { constant: MESSAGE_TYPE_IDS.MINER_DISCOVERY, expectedId: 6 },
      { constant: MESSAGE_TYPE_IDS.MINER_ALLOCATION, expectedId: 7 },
      { constant: MESSAGE_TYPE_IDS.MINER_DEALLOCATION, expectedId: 8 },
    ];

    expectedMappings.forEach(({ constant, expectedId }) => {
      expect(constant).toBe(expectedId);
    });
  });

  it('should have PROCESSING_STATUS_IDS that match expected database values', () => {
    // These should match the actual database lookup table values
    const expectedMappings = [
      { constant: PROCESSING_STATUS_IDS.PENDING, expectedId: 1 },
      { constant: PROCESSING_STATUS_IDS.PROCESSED, expectedId: 2 },
      { constant: PROCESSING_STATUS_IDS.FAILED, expectedId: 3 },
    ];

    expectedMappings.forEach(({ constant, expectedId }) => {
      expect(constant).toBe(expectedId);
    });
  });
});

describe('Entity Constraints', () => {
  it('should enforce primary key constraint on messageTypeId', () => {
    const entity = new ValidatorMessageTypeLu();
    entity.messageTypeId = 1;
    entity.messageType = 'test_type';

    // Primary key should be required (non-null)
    expect(entity.messageTypeId).toBeDefined();
    expect(typeof entity.messageTypeId).toBe('number');
  });

  it('should enforce unique constraint on messageType', () => {
    const entity1 = new ValidatorMessageTypeLu();
    entity1.messageTypeId = 1;
    entity1.messageType = 'unique_type';

    const entity2 = new ValidatorMessageTypeLu();
    entity2.messageTypeId = 2;
    entity2.messageType = 'unique_type'; // This would violate unique constraint in DB

    // Both entities can be created, but DB would reject duplicate messageType
    expect(entity1.messageType).toBe(entity2.messageType);
    expect(entity1.messageTypeId).not.toBe(entity2.messageTypeId);
  });

  it('should enforce primary key constraint on processingStatusId', () => {
    const entity = new ValidatorProcessingStatusLu();
    entity.processingStatusId = 1;
    entity.processingStatus = 'test_status';

    // Primary key should be required (non-null)
    expect(entity.processingStatusId).toBeDefined();
    expect(typeof entity.processingStatusId).toBe('number');
  });

  it('should enforce unique constraint on processingStatus', () => {
    const entity1 = new ValidatorProcessingStatusLu();
    entity1.processingStatusId = 1;
    entity1.processingStatus = 'unique_status';

    const entity2 = new ValidatorProcessingStatusLu();
    entity2.processingStatusId = 2;
    entity2.processingStatus = 'unique_status'; // This would violate unique constraint in DB

    // Both entities can be created, but DB would reject duplicate processingStatus
    expect(entity1.processingStatus).toBe(entity2.processingStatus);
    expect(entity1.processingStatusId).not.toBe(entity2.processingStatusId);
  });
});
