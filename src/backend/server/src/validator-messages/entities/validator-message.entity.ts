import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { PubSubMessage } from '../../pubsub/interfaces/pubsub-messages.interface';
import { ValidatorMessageTypeLu, ValidatorProcessingStatusLu } from './validator-message-lookup.entity';

export enum ProcessingStatus {
  PENDING = 'pending',
  PROCESSED = 'processed',
  FAILED = 'failed',
}

@Entity('validator_messages')
export class ValidatorMessage {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'message_id' })
  messageId: string;

  // Foreign key fields for normalized schema
  @Column({ name: 'message_type_id', type: 'smallint' })
  messageTypeId!: number;

  @ManyToOne(() => ValidatorMessageTypeLu, { eager: false })
  @JoinColumn({ name: 'message_type_id', referencedColumnName: 'messageTypeId' })
  messageTypeRef?: ValidatorMessageTypeLu;

  @Column({ name: 'processing_status_id', type: 'smallint' })
  processingStatusId!: number;

  @ManyToOne(() => ValidatorProcessingStatusLu, { eager: false })
  @JoinColumn({ name: 'processing_status_id', referencedColumnName: 'processingStatusId' })
  processingStatusRef?: ValidatorProcessingStatusLu;

  @Column({ name: 'validator_hotkey', type: 'varchar', length: 255 })
  validatorHotkey: string;

  @Column({ name: 'occurred_at', type: 'timestamptz' })
  occurredAt: Date;

  @Column({ name: 'miner_hotkey', type: 'varchar', length: 255, nullable: true })
  minerHotkey?: string;

  @Column({ name: 'allocation_uuid', type: 'varchar', length: 255, nullable: true })
  allocationUuid?: string;

  @Column({ name: 'message_data', type: 'jsonb' })
  rawMessageData: Record<string, unknown>;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @Index('idx_vm_processed_at')
  @Column({ name: 'processed_at', type: 'timestamptz', nullable: true })
  processedAt?: Date;

  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage?: string;

  @Column({ name: 'validator_verified', type: 'boolean', default: false })
  validatorVerified: boolean;

  @Column({ name: 'signature_valid', type: 'boolean', default: false })
  signatureValid: boolean;

  isPubSubMessage(obj: unknown): obj is PubSubMessage {
    if (!obj || typeof obj !== 'object') {
      return false;
    }

    const candidate = obj as Record<string, unknown>;
    return (
      'message_type' in candidate &&
      'source' in candidate &&
      'data' in candidate &&
      'timestamp' in candidate &&
      typeof candidate.message_type === 'string' &&
      typeof candidate.source === 'string' &&
      typeof candidate.data === 'object' &&
      candidate.data !== null &&
      typeof candidate.timestamp === 'string'
    );
  }
  // expose as strongly typed
  get messageData(): PubSubMessage {
    if (this.isPubSubMessage(this.rawMessageData)) {
      return this.rawMessageData;
    } else {
      throw new Error('Invalid PubSubMessage payload in DB');
    }
  }

  set messageData(value: PubSubMessage) {
    this.rawMessageData = value as unknown as Record<string, unknown>;
  }

  /**
   * Get the message type as string from the lookup relationship
   */
  getMessageTypeString(): string | undefined {
    return this.messageTypeRef?.messageType;
  }

  /**
   * Get the processing status as string from the lookup relationship
   */
  getProcessingStatusString(): string | undefined {
    return this.processingStatusRef?.processingStatus;
  }
}
