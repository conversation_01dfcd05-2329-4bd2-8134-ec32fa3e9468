import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import * as Redis from 'ioredis';
import { EnvironmentUtils } from '../core/utils/environment.utils';
import { NamespacedKeyUtil } from '../core/utils/namespaced-key.util';
import { RedisClientService } from '../redis';

@Injectable()
export class LeaderElectionService implements OnModuleDestroy {
  private readonly logger = new Logger(LeaderElectionService.name);
  private client: Redis.Cluster | null = null;

  constructor(private readonly redisClientService: RedisClientService) {
    if (EnvironmentUtils.isDevelopmentMode()) {
      this.logger.log('Development environment detected. Skipping Leader Election.');
      return;
    }

    if (!EnvironmentUtils.isLeaderElectionEnabled()) {
      this.logger.log('Redis-based leader election is disabled. Skipping Leader Election.');
      return;
    }

    this.initializeRedisClient();
  }

  private initializeRedisClient() {
    try {
      this.client = this.redisClientService.getClient();
      if (!this.client) {
        this.logger.error('Redis client is unavailable. Leader election is disabled.');
      } else {
        this.logger.log('LeaderElectionService initialized successfully.');
      }
    } catch (error) {
      this.logger.error('Failed to initialize Redis client for leader election:', error);
    }
  }

  async electLeader(jobName: string, instanceId: string): Promise<boolean> {
    if (EnvironmentUtils.isDevelopmentMode()) return true;
    if (!EnvironmentUtils.isLeaderElectionEnabled()) return true;
    if (!this.client) return false;

    const leaderKey = NamespacedKeyUtil.leader(jobName);
    let retryCount = 0;

    while (retryCount < 3) {
      try {
        const currentLeader = await this.client.get(leaderKey);

        if (currentLeader) {
          if (currentLeader === instanceId) {
            this.logger.log(`[${jobName}] Already running on this instance (${instanceId}), key=${leaderKey}`);
            return false;
          } else {
            this.logger.log(`[${jobName}] Running on another instance (${currentLeader}), key=${leaderKey}`);
            return false;
          }
        }

        // @ts-expect-error: ioredis typing issue for NX + EX
        const isLeader = await this.client.set(leaderKey, instanceId, 'NX', 'EX', 30);
        if (isLeader === 'OK') {
          this.logger.log(`[${jobName}] Leader acquired by instance ${instanceId}, key=${leaderKey}`);
          return true;
        }
      } catch (error) {
        this.logger.error(`[${jobName}] Error acquiring leader lock (attempt ${retryCount + 1}), key=${leaderKey}`, error);
        retryCount++;
      }
    }

    this.logger.error(`[${jobName}] Failed to acquire leader lock after ${retryCount} attempts, key=${leaderKey}`);
    return false;
  }

  async renewLock(jobName: string, currentLeader: string): Promise<void> {
    if (EnvironmentUtils.isDevelopmentMode() || !EnvironmentUtils.isLeaderElectionEnabled() || !this.client) {
      return;
    }

    const leaderKey = NamespacedKeyUtil.leader(jobName);
    try {
      await this.client.expire(leaderKey, 30);
      this.logger.log(`[${jobName}] Lock renewed by instance ${currentLeader}, key=${leaderKey}`);
    } catch (error) {
      this.logger.error(`[${jobName}] Error renewing lock, key=${leaderKey}`, error);
    }
  }

  async releaseLock(jobName: string, currentLeader: string): Promise<void> {
    if (EnvironmentUtils.isDevelopmentMode() || !EnvironmentUtils.isLeaderElectionEnabled() || !this.client) {
      return;
    }

    const leaderKey = NamespacedKeyUtil.leader(jobName);
    try {
      await this.client.del(leaderKey);
      this.logger.log(`[${jobName}] Lock released by instance ${currentLeader}, key=${leaderKey}`);
    } catch (error) {
      this.logger.error(`[${jobName}] Error releasing lock, key=${leaderKey}`, error);
    }
  }

  startLockRenewal(jobName: string, currentLeader: string): NodeJS.Timeout | null {
    if (EnvironmentUtils.isDevelopmentMode() || !EnvironmentUtils.isLeaderElectionEnabled() || !this.client) {
      return null;
    }

    const leaderKey = NamespacedKeyUtil.leader(jobName);
    this.logger.log(`[${jobName}] Lock acquired, starting renewal for instance ${currentLeader}, key=${leaderKey}`);

    return setInterval(async () => {
      try {
        await this.renewLock(jobName, currentLeader);
      } catch (error) {
        this.logger.error(`[${jobName}] Failed to renew lock, key=${leaderKey}`, error);
      }
    }, 10 * 1000);
  }

  async handleDeadLetter(jobName: string, instanceId: string, error: Error): Promise<void> {
    const leaderKey = NamespacedKeyUtil.leader(jobName);
    this.logger.error(`[${jobName}] Error moved to Dead Letter Queue by instance ${instanceId}, key=${leaderKey}`, error);
  }

  async handleJobSuccess(jobName: string, startTime: number, currentLeader: string) {
    const leaderKey = NamespacedKeyUtil.leader(jobName);
    const executionTime = Date.now() - startTime;
    this.logger.log(`[${jobName}] Completed on instance ${currentLeader} in ${executionTime} ms, key=${leaderKey}`);
    if (executionTime > 60000) {
      this.logger.warn(`[${jobName}] WARNING: Took more than 1 minute (${executionTime} ms), key=${leaderKey}`);
    }
  }

  async checkHealth(instanceId: string): Promise<void> {
    this.logger.log(`Instance ${instanceId} is healthy.`);
  }

  async onModuleDestroy() {
    try {
      this.logger.log('LeaderElectionService shutting down. Cleaning up locks...');
    } catch (error) {
      this.logger.error('Error during shutdown cleanup:', error);
    }
  }
}
